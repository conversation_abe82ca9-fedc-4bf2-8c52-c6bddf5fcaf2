"use client";

import { BusinessCard } from "./BusinessCard";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

export function FeaturedBusinesses() {
  // Get featured businesses from Convex (now with mock data)
  const featuredBusinesses = useQuery(api.businesses.getFeaturedBusinesses, { limit: 8 });

  if (!featuredBusinesses) {
    return (
      <section>
        <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
          Featured Businesses
        </h2>
        <div className="overflow-x-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
          <div className="flex items-stretch p-4 gap-3 w-max">
            {/* Loading skeleton */}
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex h-full flex-col gap-4 rounded-lg w-60 flex-shrink-0 bg-muted animate-pulse">
                <div className="w-full aspect-video bg-muted-foreground/20 rounded-xl" />
                <div className="p-4 space-y-2">
                  <div className="h-4 bg-muted-foreground/20 rounded w-3/4" />
                  <div className="h-3 bg-muted-foreground/20 rounded w-full" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section>
      <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
        Featured Businesses
      </h2>
      <div className="overflow-x-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
        <div className="flex items-stretch p-4 gap-3 w-max">
          {featuredBusinesses.map((business) => (
            <BusinessCard
              key={business._id}
              name={business.name}
              description={business.description || ""}
              imageUrl={business.imageUrl || ""}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
